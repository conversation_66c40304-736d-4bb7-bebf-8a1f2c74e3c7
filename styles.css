/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.logo i {
    margin-right: 0.5rem;
    color: #e74c3c;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #e74c3c;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #e74c3c;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-content {
    flex: 1;
    z-index: 2;
    position: relative;
    padding: 2rem;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateY(50px);
    animation: slideUp 1s ease forwards;
}

.title-line:nth-child(2) {
    animation-delay: 0.3s;
}

.highlight {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0;
    animation: slideUp 1s ease 0.6s forwards;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    opacity: 0;
    animation: slideUp 1s ease 0.9s forwards;
}

.btn {
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(231, 76, 60, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #333;
    transform: translateY(-3px);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.car-showcase {
    position: relative;
    width: 500px;
    height: 300px;
}

.car-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: scale(0.8) rotateY(45deg);
    transition: all 1s ease;
}

.car-card.active {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
}

.car-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

@keyframes slideUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #7f8c8d;
    max-width: 600px;
    margin: 0 auto;
}

/* Featured Cars Section */
.featured-cars {
    padding: 6rem 0;
    background: #f8f9fa;
}

.cars-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.car-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.car-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.car-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.car-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.car-item:hover .car-image img {
    transform: scale(1.1);
}

.car-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.car-item:hover .car-overlay {
    opacity: 1;
}

.view-details {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-details:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
}

.car-info {
    padding: 2rem;
}

.car-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.car-specs {
    color: #7f8c8d;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.car-price {
    font-size: 1.8rem;
    font-weight: 700;
    color: #e74c3c;
    margin-bottom: 1rem;
}

.car-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Services Section */
.services {
    padding: 6rem 0;
    background: white;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-item {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.service-item:hover::before {
    opacity: 1;
}

.service-item:hover {
    color: white;
    transform: translateY(-5px);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.service-item:hover .service-icon {
    background: white;
    color: #e74c3c;
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-item:hover .service-icon i {
    color: #e74c3c;
}

.service-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.service-item:hover h3 {
    color: white;
}

.service-item p {
    color: #7f8c8d;
    line-height: 1.6;
}

.service-item:hover p {
    color: rgba(255, 255, 255, 0.9);
}

/* About Section */
.about {
    padding: 6rem 0;
    background: #f8f9fa;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text .section-title {
    text-align: left;
    margin-bottom: 2rem;
}

.about-text .section-title::after {
    left: 0;
    transform: none;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    margin-bottom: 3rem;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #7f8c8d;
    font-weight: 500;
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Testimonials Section */
.testimonials {
    padding: 6rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.testimonials .section-title,
.testimonials .section-subtitle {
    color: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.testimonial-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    transition: transform 0.3s ease;
}

.testimonial-item:hover {
    transform: translateY(-5px);
}

.stars {
    display: flex;
    gap: 0.3rem;
    margin-bottom: 1rem;
}

.stars i {
    color: #ffd700;
    font-size: 1.2rem;
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.author-info span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Contact Section */
.contact {
    padding: 6rem 0;
    background: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 2rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-3px);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-item:hover .contact-icon {
    background: white;
}

.contact-icon i {
    color: white;
    font-size: 1.2rem;
}

.contact-item:hover .contact-icon i {
    color: #e74c3c;
}

.contact-details h3 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.contact-item:hover .contact-details h3 {
    color: white;
}

.contact-details p {
    color: #7f8c8d;
    line-height: 1.6;
}

.contact-item:hover .contact-details p {
    color: rgba(255, 255, 255, 0.9);
}

/* Contact Form */
.contact-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 20px;
}

.form-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group:last-of-type {
    grid-template-columns: 1fr;
}

.contact-form input,
.contact-form select,
.contact-form textarea {
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #e74c3c;
}

.contact-form textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-right: 0.5rem;
    color: #e74c3c;
    font-size: 1.8rem;
}

.footer-section p {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #34495e;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #e74c3c;
    transform: translateY(-3px);
}

.footer-section h3 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #e74c3c;
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
    color: #bdc3c7;
}

.footer-contact i {
    color: #e74c3c;
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 2rem;
    text-align: center;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .car-showcase {
        width: 300px;
        height: 200px;
        margin-top: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .cars-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-group {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .car-showcase {
        width: 250px;
        height: 150px;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: 2rem;
    }
}